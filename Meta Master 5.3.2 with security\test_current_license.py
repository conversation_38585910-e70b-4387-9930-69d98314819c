"""
Test current license key compatibility
"""

import os
import sys

def test_current_license():
    """Test the current license key"""
    print("🔑 Testing Current License Key")
    print("=" * 50)
    
    try:
        from license_checker import load_license_key, check_license, get_stable_device_id, LICENSE_FILE
        
        # Check if license file exists
        print(f"📁 License file path: {LICENSE_FILE}")
        print(f"📁 License file exists: {os.path.exists(LICENSE_FILE)}")
        
        # Load current license key
        current_key = load_license_key()
        if current_key:
            print(f"🔑 Current license key: {current_key[:16]}...")
            print(f"🔑 License key length: {len(current_key)}")
        else:
            print("❌ No license key found")
            return False
        
        # Get device ID
        device_id = get_stable_device_id()
        print(f"🖥️ Current device ID: {device_id[:16]}...")
        
        # Test license validation
        print("\n🔍 Testing license validation...")
        is_valid, message = check_license()
        
        print(f"✅ License valid: {is_valid}")
        print(f"📝 Validation message: {message}")
        
        if not is_valid:
            print("\n🔍 Testing with explicit license key...")
            is_valid2, message2 = check_license(current_key)
            print(f"✅ License valid (explicit): {is_valid2}")
            print(f"📝 Validation message (explicit): {message2}")
        
        return is_valid
        
    except Exception as e:
        print(f"❌ Error testing license: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_license_in_firebase():
    """Check if the license exists in Firebase"""
    print("\n🔥 Checking License in Firebase")
    print("=" * 50)
    
    try:
        from license_checker import load_license_key
        import firebase_admin
        from firebase_admin import firestore
        
        current_key = load_license_key()
        if not current_key:
            print("❌ No license key to check")
            return False
        
        # Get Firebase client
        if firebase_admin._apps:
            db = firestore.client()
            
            # Check if license exists in Firebase
            doc_ref = db.collection("licenses").document(current_key)
            doc = doc_ref.get()
            
            if doc.exists:
                license_data = doc.to_dict()
                print("✅ License found in Firebase")
                print(f"📋 License data:")
                for key, value in license_data.items():
                    if key == 'device_id':
                        print(f"   {key}: {value[:16] if value else 'None'}...")
                    else:
                        print(f"   {key}: {value}")
                return True
            else:
                print("❌ License not found in Firebase")
                return False
        else:
            print("❌ Firebase not initialized")
            return False
            
    except Exception as e:
        print(f"❌ Error checking Firebase: {e}")
        return False

def show_device_comparison():
    """Show device ID comparison"""
    print("\n🖥️ Device ID Analysis")
    print("=" * 50)
    
    try:
        from license_checker import get_stable_device_id, load_device_id
        
        current_device = get_stable_device_id()
        stored_device = load_device_id()
        
        print(f"🖥️ Current device ID: {current_device}")
        print(f"💾 Stored device ID: {stored_device}")
        
        if current_device == stored_device:
            print("✅ Device IDs match")
            return True
        else:
            print("❌ Device IDs don't match")
            print("💡 This might be why your license isn't working")
            return False
            
    except Exception as e:
        print(f"❌ Error comparing device IDs: {e}")
        return False

def suggest_fixes():
    """Suggest fixes for license issues"""
    print("\n🔧 Suggested Fixes")
    print("=" * 50)
    
    print("If your license isn't working, try these solutions:")
    print()
    print("1. 🔄 Reset device binding:")
    print("   - Delete the device ID file")
    print("   - Re-activate your license")
    print()
    print("2. 🔑 Re-enter license key:")
    print("   - Delete the license file")
    print("   - Enter your license key again")
    print()
    print("3. 🛡️ Disable enhanced security temporarily:")
    print("   - Use the original license validation")
    print("   - Skip online validation")
    print()
    print("4. 📞 Contact support:")
    print("   - Provide your license key")
    print("   - Provide your device ID")

def main():
    """Run license compatibility test"""
    print("🔑 License Compatibility Test")
    print("=" * 60)
    
    # Test current license
    license_works = test_current_license()
    
    # Check Firebase
    firebase_works = check_license_in_firebase()
    
    # Check device IDs
    device_match = show_device_comparison()
    
    # Show results
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"   License Validation: {'✅ WORKING' if license_works else '❌ FAILED'}")
    print(f"   Firebase Check: {'✅ WORKING' if firebase_works else '❌ FAILED'}")
    print(f"   Device ID Match: {'✅ MATCH' if device_match else '❌ MISMATCH'}")
    
    if license_works:
        print("\n🎉 Your license is working correctly!")
        print("💡 You should be able to use Meta Master normally.")
    else:
        print("\n⚠️ License issue detected!")
        suggest_fixes()
    
    return license_works

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
