import os
import sys
import datetime
import webbrowser  # Opens the WhatsApp link
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from ttkbootstrap.dialogs import Messagebox
import subprocess

# Try to import Firebase with error handling
try:
    import firebase_admin
    from firebase_admin import credentials, firestore
    FIREBASE_AVAILABLE = True
except ImportError as e:
    print(f"Firebase not available: {e}")
    FIREBASE_AVAILABLE = False
    firebase_admin = None
    credentials = None
    firestore = None

def resource_path(relative_path):
    """ Get absolute path to resource, works for both development & PyInstaller EXE """
    if getattr(sys, 'frozen', False):  # Running as an EXE
        base_path = sys._MEIPASS
    else:
        base_path = os.path.dirname(__file__)  # Running as Python script
    return os.path.join(base_path, relative_path)

# Import secure configuration
try:
    from secure_config import secure_config
    from runtime_protection import runtime_protection
    from integrity_verification import integrity_verifier
    SECURITY_AVA<PERSON>ABLE = True
except ImportError:
    print("Security modules not available - running in basic mode")
    SECURITY_AVAILABLE = False
    secure_config = None
    runtime_protection = None
    integrity_verifier = None

# Initialize Firebase with error handling
db = None
firebase_initialized = False

def initialize_firebase():
    """Initialize Firebase with secure configuration and error handling."""
    global db, firebase_initialized

    if firebase_initialized:
        return True

    if not FIREBASE_AVAILABLE:
        print("Firebase SDK is not available. Please install firebase-admin package.")
        return False

    # Start runtime protection if available
    if SECURITY_AVAILABLE and runtime_protection:
        runtime_protection.start_protection()

    # Verify application integrity if available
    if SECURITY_AVAILABLE and integrity_verifier:
        verification_results = integrity_verifier.perform_full_verification()
        if not verification_results["overall_status"]:
            print("⚠️  Application integrity compromised. Please reinstall from official source.")
            return False

    try:
        firebase_json_path = None

        # Try to get secure configuration first
        if SECURITY_AVAILABLE and secure_config:
            firebase_json_path = secure_config.get_firebase_config_path()

        # Fallback to local file
        if not firebase_json_path:
            firebase_json_path = resource_path("meta-master-firebase.json")

            # Check if local file exists and is valid
            if not os.path.exists(firebase_json_path):
                print(f"Firebase config file not found: {firebase_json_path}")
                return False

            # Read and validate the Firebase JSON file
            with open(firebase_json_path, 'r') as f:
                firebase_config = f.read()

            # Check if the file contains placeholder values
            if '"encrypted"' in firebase_config or 'encrypted' in firebase_config:
                print("Firebase config contains placeholder values. Using secure configuration.")
                return False

        # Initialize Firebase
        if firebase_json_path and os.path.exists(firebase_json_path):
            # Avoid repeated Firebase initialization
            if not firebase_admin._apps:
                cred = credentials.Certificate(firebase_json_path)
                firebase_admin.initialize_app(cred)

            db = firestore.client()
            firebase_initialized = True
            return True
        else:
            print("No valid Firebase configuration available.")
            return False

    except Exception as e:
        print(f"Failed to initialize Firebase: {str(e)}")
        return False

LICENSE_FILE = os.path.join(os.getenv("APPDATA"), "MetaMaster", "license.txt")

def save_license_key(license_key):
    """Save the license key in a writable directory (AppData)."""
    os.makedirs(os.path.dirname(LICENSE_FILE), exist_ok=True)  # Ensure folder exists
    with open(LICENSE_FILE, "w") as f:
        f.write(license_key)

def load_license_key():
    """Retrieve the saved license key from the file."""
    if os.path.exists(LICENSE_FILE):
        with open(LICENSE_FILE, "r") as f:
            return f.read().strip()
    return None

def get_device_id():
    """Returns a unique hardware identifier."""
    try:
        # Use a more efficient method to get the device ID
        return subprocess.check_output("wmic csproduct get uuid", shell=True).decode().split("\n")[1].strip()
    except Exception:
        return "UNKNOWN_DEVICE"

def check_license(license_key=None):
    """Validate the license key with Firebase Firestore and enforce single-device restriction."""
    # Initialize Firebase if not already done
    if not initialize_firebase():
        return False, "❌ License system unavailable. Please contact support."

    if license_key is None:
        license_key = load_license_key()

    if not license_key:
        return False, "No License Key Found!"

    try:
        doc_ref = db.collection("licenses").document(license_key)
        doc = doc_ref.get()
    except Exception as e:
        return False, f"❌ License verification failed: {str(e)}"

    if doc.exists:
        license_data = doc.to_dict()
        is_active = license_data.get("active", False)
        expiry_date = license_data.get("expiry")
        registered_device = license_data.get("device_id", None)

        # Convert expiry date to datetime object
        expiry_date_dt = datetime.datetime.strptime(expiry_date, "%Y-%m-%d")
        today = datetime.datetime.today()
        remaining_days = (expiry_date_dt - today).days

        current_device = get_device_id()

        if not is_active:
            return False, "❌ This license has been deactivated!"

        if remaining_days <= 0:
            return False, "❌ License Expired! Please renew."

        if registered_device is None:
            # First-time activation: Bind license to this device
            doc_ref.update({"device_id": current_device})
            return True, f"✅ License Activated and Bound to this Device!"

        if registered_device == current_device:
            return True, f"✅ License is valid! 🔹 Days Remaining: {remaining_days} days"

        return False, "❌ License already used on another device!"
    else:
        return False, "❌ Invalid License Key! Contact support."


def activate_license(root, entry):
    """Allow the user to enter a license key, validate it, and restrict to one device."""
    # Initialize Firebase if not already done
    if not initialize_firebase():
        Messagebox.show_error("❌ License system unavailable. Please contact support.", "System Error")
        return

    license_key = entry.get().strip()
    current_device = get_device_id()  # Get unique device ID
    is_valid, message = check_license(license_key)

    if is_valid:
        try:
            # Save the license key locally
            save_license_key(license_key)

            # Update the license in Firebase with the device ID
            doc_ref = db.collection("licenses").document(license_key)
            doc_ref.update({"device_id": current_device})  # Bind the license to this device

            Messagebox.show_info("🎉 Activation Successful!\n" + message, "License Activated")
            root.destroy()  # Close the License Window
        except Exception as e:
            Messagebox.show_error(f"❌ Activation failed: {str(e)}", "Activation Error")
    else:
        Messagebox.show_error(message, "Activation Failed")

def show_license_input_window():
    """Display a window to input the license key."""
    root = ttk.Window(themename="superhero")
    root.title("License Activation")
    root.geometry("400x250")
    root.resizable(False, False)

    ttk.Label(root, text="Enter Your License Key:", font=("Arial", 12, "bold")).pack(pady=10)
    entry = ttk.Entry(root, width=40)
    entry.pack(pady=5)

    btn_frame = ttk.Frame(root)
    btn_frame.pack(pady=10)

    # Create the "Contact Us" button and make it always visible
    ttk.Button(
        btn_frame, 
        text="Contact Us", 
        bootstyle=INFO, 
        command=lambda: webbrowser.open("https://www.facebook.com/m.mastersoft")
    ).pack(side=LEFT, padx=5)

    ttk.Button(
        btn_frame, 
        text="Activate", 
        bootstyle=SUCCESS, 
        command=lambda: activate_license(root, entry)
    ).pack(side=LEFT, padx=5)

    root.mainloop()

# Run License Check and Open License Window If Needed
try:
    if not check_license()[0]:
        show_license_input_window()
except Exception as e:
    print(f"License check failed: {str(e)}")
    # Still show the license window if there's an error
    show_license_input_window()
