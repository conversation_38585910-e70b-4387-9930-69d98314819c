# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['E:\\Software Buid\\Final Meta Master With License\\Meta Master.py'],
    pathex=[],
    binaries=[],
    datas=[('E:\\Software Buid\\Final Meta Master With License\\Meta Master.ico', '.'), ('E:\\Software Buid\\Final Meta Master With License\\Meta Master.png', '.'), ('E:\\Software Buid\\Final Meta Master With License\\meta-master-firebase.json', '.'), ('E:\\Software Buid\\Final Meta Master With License\\README.txt', '.'), ('E:\\Software Buid\\Final Meta Master With License\\exiftool.exe', '.'), ('E:\\Software Buid\\Final Meta Master With License\\exiftool_files', 'exiftool_files')],
    hiddenimports=['ttkbootstrap', 'cv2', 'numpy'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['tkinter.test'],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='Meta Master',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['E:\\Software Buid\\Final Meta Master With License\\Meta Master.ico'],
)
