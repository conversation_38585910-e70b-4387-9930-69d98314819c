"""
Secure Configuration Manager for Meta Master
Encrypts and protects sensitive configuration data
"""

import os
import json
import base64
import hashlib
from cryptography.fernet import Fe<PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import platform
import time
from security_protection import get_security_instance, protection_check

class SecureConfig:
    def __init__(self):
        self._config_dir = os.path.join(os.getenv("APPDATA"), "MetaMaster", ".secure")
        os.makedirs(self._config_dir, exist_ok=True)
        self._config_file = os.path.join(self._config_dir, "config.enc")
        self._key = self._generate_config_key()
        self._cipher = Fernet(self._key)
        self._config_cache = {}
        
    def _generate_config_key(self):
        """Generate encryption key for configuration"""
        try:
            # Use system-specific information for key generation
            system_info = f"{platform.machine()}{platform.processor()}{os.environ.get('USERNAME', '')}"
            password = f"metamaster_config_{system_info}_2024".encode()
            salt = b'secure_config_salt_metamaster_2024'
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=150000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password))
            return key
        except Exception:
            # Fallback key
            fallback = "metamaster_secure_config_fallback_2024"
            return base64.urlsafe_b64encode(hashlib.sha256(fallback.encode()).digest())
    
    def _encrypt_data(self, data):
        """Encrypt configuration data"""
        try:
            if isinstance(data, dict):
                data = json.dumps(data)
            if isinstance(data, str):
                data = data.encode()
            return self._cipher.encrypt(data)
        except Exception:
            return data
    
    def _decrypt_data(self, encrypted_data):
        """Decrypt configuration data"""
        try:
            if isinstance(encrypted_data, bytes):
                decrypted = self._cipher.decrypt(encrypted_data)
                return json.loads(decrypted.decode())
            return encrypted_data
        except Exception:
            return {}
    
    def save_config(self, config_data):
        """Save encrypted configuration"""
        try:
            protection_check()
            
            # Add metadata
            config_with_meta = {
                'data': config_data,
                'timestamp': time.time(),
                'checksum': hashlib.sha256(json.dumps(config_data, sort_keys=True).encode()).hexdigest()
            }
            
            encrypted_data = self._encrypt_data(config_with_meta)
            
            with open(self._config_file, 'wb') as f:
                f.write(encrypted_data)
            
            # Update cache
            self._config_cache = config_data.copy()
            return True
            
        except Exception as e:
            print(f"Config save error: {e}")
            return False
    
    def load_config(self):
        """Load and decrypt configuration"""
        try:
            protection_check()
            
            # Return cached config if available
            if self._config_cache:
                return self._config_cache
            
            if not os.path.exists(self._config_file):
                return {}
            
            with open(self._config_file, 'rb') as f:
                encrypted_data = f.read()
            
            config_with_meta = self._decrypt_data(encrypted_data)
            
            if not isinstance(config_with_meta, dict):
                return {}
            
            # Verify integrity
            config_data = config_with_meta.get('data', {})
            stored_checksum = config_with_meta.get('checksum', '')
            calculated_checksum = hashlib.sha256(json.dumps(config_data, sort_keys=True).encode()).hexdigest()
            
            if stored_checksum != calculated_checksum:
                print("Config integrity check failed")
                return {}
            
            # Cache the config
            self._config_cache = config_data.copy()
            return config_data
            
        except Exception as e:
            print(f"Config load error: {e}")
            return {}
    
    def get_secure_value(self, key, default=None):
        """Get a specific configuration value"""
        try:
            config = self.load_config()
            return config.get(key, default)
        except Exception:
            return default
    
    def set_secure_value(self, key, value):
        """Set a specific configuration value"""
        try:
            config = self.load_config()
            config[key] = value
            return self.save_config(config)
        except Exception:
            return False
    
    def clear_config(self):
        """Clear all configuration data"""
        try:
            if os.path.exists(self._config_file):
                os.remove(self._config_file)
            self._config_cache = {}
            return True
        except Exception:
            return False

# Encrypted storage for sensitive API keys and credentials
class SecureCredentials:
    def __init__(self):
        self._cred_dir = os.path.join(os.getenv("APPDATA"), "MetaMaster", ".credentials")
        os.makedirs(self._cred_dir, exist_ok=True)
        self._security = get_security_instance()
        
    def store_api_key(self, service_name, api_key):
        """Store encrypted API key"""
        try:
            protection_check()
            
            encrypted_key = self._security.encrypt_string(api_key)
            key_file = os.path.join(self._cred_dir, f"{service_name}.key")
            
            with open(key_file, 'wb') as f:
                f.write(encrypted_key)
            
            return True
        except Exception:
            return False
    
    def get_api_key(self, service_name):
        """Retrieve and decrypt API key"""
        try:
            protection_check()
            
            key_file = os.path.join(self._cred_dir, f"{service_name}.key")
            
            if not os.path.exists(key_file):
                return None
            
            with open(key_file, 'rb') as f:
                encrypted_key = f.read()
            
            return self._security.decrypt_string(encrypted_key)
        except Exception:
            return None
    
    def store_firebase_config(self, config_data):
        """Store encrypted Firebase configuration"""
        try:
            protection_check()
            
            # Encrypt the entire Firebase config
            encrypted_config = self._security.encrypt_string(json.dumps(config_data))
            config_file = os.path.join(self._cred_dir, "firebase.enc")
            
            with open(config_file, 'wb') as f:
                f.write(encrypted_config)
            
            return True
        except Exception:
            return False
    
    def get_firebase_config(self):
        """Retrieve and decrypt Firebase configuration"""
        try:
            protection_check()
            
            config_file = os.path.join(self._cred_dir, "firebase.enc")
            
            if not os.path.exists(config_file):
                return None
            
            with open(config_file, 'rb') as f:
                encrypted_config = f.read()
            
            decrypted_config = self._security.decrypt_string(encrypted_config)
            return json.loads(decrypted_config)
        except Exception:
            return None
    
    def clear_all_credentials(self):
        """Clear all stored credentials"""
        try:
            import shutil
            if os.path.exists(self._cred_dir):
                shutil.rmtree(self._cred_dir)
            os.makedirs(self._cred_dir, exist_ok=True)
            return True
        except Exception:
            return False

# Global instances
_secure_config = None
_secure_credentials = None

def get_secure_config():
    """Get secure configuration manager instance"""
    global _secure_config
    if _secure_config is None:
        _secure_config = SecureConfig()
    return _secure_config

def get_secure_credentials():
    """Get secure credentials manager instance"""
    global _secure_credentials
    if _secure_credentials is None:
        _secure_credentials = SecureCredentials()
    return _secure_credentials
