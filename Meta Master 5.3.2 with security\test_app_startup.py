"""
Test Meta Master application startup
"""

import sys
import os

def test_license_validation():
    """Test license validation"""
    print("🔑 Testing license validation...")
    
    try:
        from license_checker import check_license
        
        is_valid, message = check_license()
        print(f"✅ License valid: {is_valid}")
        print(f"📝 Message: {message}")
        
        return is_valid
        
    except Exception as e:
        print(f"❌ License validation error: {e}")
        return False

def test_security_initialization():
    """Test security system initialization"""
    print("\n🛡️ Testing security initialization...")
    
    try:
        from security_protection import get_security_instance, protection_check
        from runtime_protection import initialize_runtime_protection, comprehensive_protection_check
        from integrity_verification import initialize_integrity_verification, verify_application_integrity
        
        # Test protection check
        protection_check()
        print("✅ Protection check passed")
        
        # Test runtime protection
        if initialize_runtime_protection():
            print("✅ Runtime protection initialized")
        else:
            print("❌ Runtime protection failed")
            return False
        
        # Test comprehensive protection
        if comprehensive_protection_check():
            print("✅ Comprehensive protection check passed")
        else:
            print("❌ Comprehensive protection check failed")
            return False
        
        # Test integrity verification
        if initialize_integrity_verification():
            print("✅ Integrity verification initialized")
        else:
            print("❌ Integrity verification failed")
            return False
        
        # Test application integrity
        is_valid, message = verify_application_integrity()
        print(f"📋 Application integrity: {message}")
        
        return is_valid
        
    except Exception as e:
        print(f"❌ Security initialization error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_imports():
    """Test critical imports"""
    print("\n📦 Testing critical imports...")
    
    try:
        # Test security imports
        from security_protection import get_security_instance, protection_check
        from secure_config import get_secure_config, get_secure_credentials
        from runtime_protection import runtime_protect, initialize_runtime_protection
        from integrity_verification import initialize_integrity_verification
        from license_checker import check_license, get_stable_device_id, LICENSE_FILE
        print("✅ Security modules imported successfully")
        
        # Test main application imports
        import tkinter as tk
        import ttkbootstrap as ttk
        from PIL import Image, ImageTk
        import google.generativeai as genai
        print("✅ Main application modules imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_startup_sequence():
    """Simulate the Meta Master startup sequence"""
    print("\n🚀 Simulating Meta Master startup sequence...")
    
    try:
        # Step 1: Security initialization
        print("   1. Initializing security protection...")
        from security_protection import get_security_instance, protection_check
        
        security_instance = get_security_instance()
        protection_check()
        
        env_valid, env_message = security_instance.verify_execution_environment()
        if not env_valid:
            print(f"   ❌ Environment check failed: {env_message}")
            return False
        print("   ✅ Security protection initialized")
        
        # Step 2: Runtime protection
        print("   2. Initializing runtime protection...")
        from runtime_protection import initialize_runtime_protection, comprehensive_protection_check
        
        if not initialize_runtime_protection():
            print("   ❌ Runtime protection initialization failed")
            return False
        
        if not comprehensive_protection_check():
            print("   ❌ Comprehensive protection check failed")
            return False
        print("   ✅ Runtime protection initialized")
        
        # Step 3: Integrity verification
        print("   3. Initializing integrity verification...")
        from integrity_verification import initialize_integrity_verification, verify_application_integrity
        
        if not initialize_integrity_verification():
            print("   ❌ Integrity verification initialization failed")
            return False
        
        integrity_valid, integrity_message = verify_application_integrity()
        if not integrity_valid:
            print(f"   ❌ Application integrity check failed: {integrity_message}")
            return False
        print("   ✅ Integrity verification initialized")
        
        # Step 4: License validation
        print("   4. Validating license...")
        from license_checker import check_license
        
        protection_check()  # Verify security protection is active
        license_valid, license_message = check_license()
        
        if not license_valid:
            print(f"   ❌ License validation failed: {license_message}")
            return False
        
        protection_check()  # Additional security verification
        print(f"   ✅ License validated: {license_message}")
        
        print("\n🎉 Startup sequence completed successfully!")
        return True
        
    except Exception as e:
        print(f"   ❌ Startup sequence error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run startup tests"""
    print("🚀 Meta Master Startup Test")
    print("=" * 50)
    
    tests = [
        ("Critical Imports", test_imports),
        ("License Validation", test_license_validation),
        ("Security Initialization", test_security_initialization),
        ("Startup Sequence", simulate_startup_sequence)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            print(f"\n{'✅' if result else '❌'} {test_name}: {'PASSED' if result else 'FAILED'}")
        except Exception as e:
            print(f"\n❌ {test_name}: ERROR - {e}")
            results[test_name] = False
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    all_passed = all(results.values())
    print(f"\n🎯 Overall: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🎉 Meta Master should start successfully!")
        print("💡 You can now run: python \"Meta Master.py\"")
    else:
        print("\n⚠️ Some tests failed. Please check the issues above.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
