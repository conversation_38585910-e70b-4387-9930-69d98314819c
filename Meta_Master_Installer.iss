; Inno Setup Script for Meta Master - Secure Professional Installer

[Setup]
AppName=Meta Master
AppVersion=5.1.1
AppPublisher=Meta Master Software
AppPublisherURL=https://getmetamaster.com
AppSupportURL=https://getmetamaster.com/support
AppUpdatesURL=https://getmetamaster.com/updates
DefaultDirName={autopf}\Meta Master
DefaultGroupName=Meta Master
AllowNoIcons=yes
OutputDir=Output
OutputBaseFilename=Meta_Master_Setup_5.1.1
SetupIconFile="Meta Master.ico"
Compression=lzma2/ultra64
SolidCompression=yes
WizardStyle=modern
UninstallDisplayIcon={app}\Meta Master.exe
ArchitecturesAllowed=x64compatible
ArchitecturesInstallIn64BitMode=x64compatible
; Security settings
PrivilegesRequired=lowest
PrivilegesRequiredOverridesAllowed=dialog
DisableDirPage=no
DisableProgramGroupPage=no
; Signing (uncomment when you have a code signing certificate)
; SignTool=signtool
; SignedUninstaller=yes

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"
Name: "french"; MessagesFile: "compiler:Languages\French.isl"

[Files]
; Main application files
Source: "dist\Meta Master\Meta Master.exe"; DestDir: "{app}"; Flags: ignoreversion signonce
Source: "dist\Meta Master\Meta Master.ico"; DestDir: "{app}"; Flags: ignoreversion
Source: "dist\Meta Master\Meta Master.png"; DestDir: "{app}"; Flags: ignoreversion
Source: "dist\Meta Master\README.txt"; DestDir: "{app}"; Flags: ignoreversion
Source: "dist\Meta Master\LICENSE.txt"; DestDir: "{app}"; Flags: ignoreversion

; External tools
Source: "dist\Meta Master\exiftool.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "dist\Meta Master\exiftool_files\*"; DestDir: "{app}\exiftool_files"; Flags: ignoreversion recursesubdirs

; Encrypted configuration (only if exists - no plain Firebase config)
Source: "dist\Meta Master\config.enc"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist

; Security note: meta-master-firebase.json is intentionally excluded for security

[Icons]
Name: "{commondesktop}\Meta Master"; Filename: "{app}\Meta Master.exe"; IconFilename: "{app}\Meta Master.ico"
Name: "{group}\Meta Master"; Filename: "{app}\Meta Master.exe"; IconFilename: "{app}\Meta Master.ico"

[Run]
Filename: "{app}\Meta Master.exe"; Description: "Launch Meta Master"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
Type: filesandordirs; Name: "{app}"

[Registry]
Root: HKLM; Subkey: "Software\MetaMaster"; ValueType: string; ValueName: "Install_Dir"; ValueData: "{app}"; Flags: uninsdeletekey
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\App Paths\Meta Master.exe"; ValueType: string; ValueName: ""; ValueData: "{app}\Meta Master.exe"; Flags: uninsdeletekey
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\App Paths\Meta Master.exe"; ValueType: string; ValueName: "Path"; ValueData: "{app}"; Flags: uninsdeletekey

[Code]
var
  LicenseAccepted: Boolean;

function InitializeSetup(): Boolean;
begin
  Result := True;
  LicenseAccepted := False;

  // Check if running in a virtual machine (basic detection)
  if (GetEnv('PROCESSOR_IDENTIFIER') <> '') and
     ((Pos('VBOX', UpperCase(GetEnv('PROCESSOR_IDENTIFIER'))) > 0) or
      (Pos('VMWARE', UpperCase(GetEnv('PROCESSOR_IDENTIFIER'))) > 0)) then
  begin
    if MsgBox('Virtual machine detected. Continue installation?', mbConfirmation, MB_YESNO) = IDNO then
    begin
      Result := False;
      Exit;
    end;
  end;

  // Check for minimum system requirements
  if not IsWin64 then
  begin
    MsgBox('This application requires a 64-bit version of Windows.', mbError, MB_OK);
    Result := False;
    Exit;
  end;
end;

function NextButtonClick(CurPageID: Integer): Boolean;
begin
  Result := True;

  // Custom license validation
  if CurPageID = wpLicense then
  begin
    LicenseAccepted := True;
  end;

  // Pre-installation checks
  if CurPageID = wpReady then
  begin
    if not LicenseAccepted then
    begin
      MsgBox('You must accept the license agreement to continue.', mbError, MB_OK);
      Result := False;
      Exit;
    end;

    // Check for existing installation
    if FileExists(ExpandConstant('{app}\Meta Master.exe')) then
    begin
      if MsgBox('An existing installation was found. Do you want to continue?', mbConfirmation, MB_YESNO) = IDNO then
      begin
        Result := False;
        Exit;
      end;
    end;
  end;
end;

procedure CurStepChanged(CurStep: TSetupStep);
var
  ResultCode: Integer;
begin
  if CurStep = ssInstall then
  begin
    // Pre-installation cleanup
    if FileExists(ExpandConstant('{app}\meta-master-firebase.json')) then
    begin
      DeleteFile(ExpandConstant('{app}\meta-master-firebase.json'));
    end;
  end;

  if CurStep = ssDone then
  begin
    // Post-installation setup
    MsgBox('Meta Master has been successfully installed!' + #13#10 +
           'Please visit https://getmetamaster.com for license activation.' + #13#10 +
           'Thank you for using our software!', mbInformation, MB_OK);

    // Optional: Open website for license purchase
    if MsgBox('Would you like to visit our website to purchase a license?', mbConfirmation, MB_YESNO) = IDYES then
    begin
      ShellExec('open', 'https://getmetamaster.com/pricing', '', '', SW_SHOWNORMAL, ewNoWait, ResultCode);
    end;
  end;
end;

procedure CurUninstallStepChanged(CurUninstallStep: TUninstallStep);
begin
  if CurUninstallStep = usPostUninstall then
  begin
    // Clean up user data (optional)
    if MsgBox('Do you want to remove all user data and settings?', mbConfirmation, MB_YESNO) = IDYES then
    begin
      DelTree(ExpandConstant('{userappdata}\MetaMaster'), True, True, True);
    end;
  end;
end;
