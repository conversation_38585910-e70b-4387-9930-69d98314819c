# -*- mode: python ; coding: utf-8 -*-
"""
Secure PyInstaller Specification for Meta Master
Includes security measures and proper file handling
"""

import os
import sys

# Security configuration
EXCLUDE_SENSITIVE_FILES = True
ENABLE_UPX_COMPRESSION = True
ENABLE_OBFUSCATION = True

# Get the current directory
current_dir = os.path.dirname(os.path.abspath(SPEC))

# Define paths
main_script = os.path.join(current_dir, 'Meta Master.py')
icon_file = os.path.join(current_dir, 'Meta Master.ico')

# Data files to include (excluding sensitive files)
data_files = [
    (os.path.join(current_dir, 'Meta Master.ico'), '.'),
    (os.path.join(current_dir, 'Meta Master.png'), '.'),
    (os.path.join(current_dir, 'README.txt'), '.'),
    (os.path.join(current_dir, 'exiftool.exe'), '.'),
    (os.path.join(current_dir, 'exiftool_files'), 'exiftool_files'),
]

# Only include encrypted config if it exists (not the plain Firebase JSON)
encrypted_config = os.path.join(current_dir, 'config.enc')
if os.path.exists(encrypted_config):
    data_files.append((encrypted_config, '.'))

# Hidden imports for security and functionality
hidden_imports = [
    'ttkbootstrap',
    'cv2',
    'numpy',
    'firebase_admin',
    'firebase_admin.credentials',
    'firebase_admin.firestore',
    'cryptography',
    'cryptography.fernet',
    'cryptography.hazmat.primitives',
    'cryptography.hazmat.primitives.kdf.pbkdf2',
    'psutil',
    'requests',
    'secure_config',
    'runtime_protection',
    'integrity_verification',
]

# Files to exclude from the build
excludes = [
    'tkinter.test',
    'unittest',
    'test',
    'tests',
    'pytest',
    'setuptools',
    'distutils',
    'pip',
    'wheel',
    # Exclude development and debugging tools
    'pdb',
    'pydoc',
    'doctest',
    'inspect',
    'dis',
    'trace',
    'profile',
    'cProfile',
    'pstats',
]

# Exclude sensitive files from being bundled
if EXCLUDE_SENSITIVE_FILES:
    excludes.extend([
        'meta-master-firebase.json',  # Don't include plain Firebase config
        'firebase_config.json',
        'credentials.json',
        '.env',
        'config.json',
    ])

a = Analysis(
    [main_script],
    pathex=[current_dir],
    binaries=[],
    datas=data_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    noarchive=False,
    optimize=2,  # Maximum optimization
)

# Remove sensitive files from the analysis if they were accidentally included
if EXCLUDE_SENSITIVE_FILES:
    sensitive_patterns = [
        'firebase.json',
        'credentials.json',
        'config.json',
        '.env'
    ]
    
    a.datas = [
        (dest, source, kind) for dest, source, kind in a.datas
        if not any(pattern in dest.lower() or pattern in source.lower() 
                  for pattern in sensitive_patterns)
    ]

pyz = PYZ(a.pure, a.zipped_data)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='Meta Master',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,  # Strip debug symbols
    upx=ENABLE_UPX_COMPRESSION,  # Enable UPX compression for obfuscation
    upx_exclude=[
        # Exclude certain files from UPX compression if they cause issues
        'vcruntime140.dll',
        'python3.dll',
    ],
    runtime_tmpdir=None,
    console=False,  # No console window
    disable_windowed_traceback=True,  # Disable traceback in windowed mode
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_file,
    # Additional security options
    uac_admin=False,  # Don't require admin privileges
    uac_uiaccess=False,
    # Manifest options for Windows
    manifest=None,
)
