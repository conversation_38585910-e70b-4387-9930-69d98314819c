"""
Application Integrity Verification System for Meta Master
Implements checksum verification and self-verification mechanisms
"""

import os
import sys
import hashlib
import time
import json
import threading
from pathlib import Path
from security_protection import get_security_instance, protection_check

class IntegrityVerifier:
    def __init__(self):
        self._security = get_security_instance()
        self._verification_active = True
        self._known_hashes = {}
        self._integrity_cache = {}
        self._last_verification = 0
        
        # Initialize verification
        self._initialize_integrity_baseline()
        
    def _initialize_integrity_baseline(self):
        """Initialize integrity baseline for critical files"""
        try:
            # Get application directory
            if hasattr(sys, 'frozen'):
                app_dir = Path(sys.executable).parent
                exe_path = sys.executable
            else:
                app_dir = Path(__file__).parent
                exe_path = None
            
            # Critical files to monitor
            critical_files = [
                'Meta Master.exe',
                'meta-master-firebase.json',
                'exiftool.exe'
            ]
            
            # Calculate hashes for critical files
            for file_name in critical_files:
                file_path = app_dir / file_name
                if file_path.exists():
                    file_hash = self._calculate_file_hash(file_path)
                    self._known_hashes[file_name] = file_hash
            
            # If running as executable, verify main executable
            if exe_path:
                exe_hash = self._calculate_file_hash(exe_path)
                self._known_hashes['main_executable'] = exe_hash
                
        except Exception as e:
            # Log error but don't fail initialization
            try:
                self._security._log_security_event(f"Integrity baseline initialization error: {str(e)}")
            except Exception:
                pass
    
    def _calculate_file_hash(self, file_path):
        """Calculate SHA256 hash of a file"""
        try:
            hash_sha256 = hashlib.sha256()
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception:
            return None
    
    def verify_file_integrity(self, file_path):
        """Verify integrity of a specific file"""
        try:
            protection_check()
            
            file_name = Path(file_path).name
            current_hash = self._calculate_file_hash(file_path)
            
            if not current_hash:
                return False, "Could not calculate file hash"
            
            # Check against known hash
            if file_name in self._known_hashes:
                expected_hash = self._known_hashes[file_name]
                if current_hash != expected_hash:
                    return False, f"File integrity compromised: {file_name}"
            
            # Update cache
            self._integrity_cache[file_name] = {
                'hash': current_hash,
                'verified_at': time.time()
            }
            
            return True, "File integrity verified"
            
        except Exception as e:
            return False, f"Integrity verification error: {str(e)}"
    
    def verify_application_integrity(self):
        """Verify integrity of the entire application"""
        try:
            protection_check()
            
            # Get application directory
            if hasattr(sys, 'frozen'):
                app_dir = Path(sys.executable).parent
                exe_path = sys.executable
            else:
                app_dir = Path(__file__).parent
                exe_path = None
            
            verification_results = []
            
            # Verify critical files
            for file_name in self._known_hashes.keys():
                if file_name == 'main_executable' and exe_path:
                    file_path = exe_path
                else:
                    file_path = app_dir / file_name
                
                if Path(file_path).exists():
                    is_valid, message = self.verify_file_integrity(file_path)
                    verification_results.append((file_name, is_valid, message))
                    
                    if not is_valid:
                        # Critical file compromised
                        self._trigger_integrity_violation(f"Critical file compromised: {file_name}")
                        return False, f"Integrity check failed: {message}"
            
            # All checks passed
            self._last_verification = time.time()
            return True, "Application integrity verified"
            
        except Exception as e:
            return False, f"Application integrity check failed: {str(e)}"
    
    def verify_memory_integrity(self):
        """Verify memory integrity and detect code injection"""
        try:
            protection_check()
            
            # Check for suspicious memory patterns
            import psutil
            current_process = psutil.Process()
            
            # Check memory usage patterns
            memory_info = current_process.memory_info()
            
            # Detect unusual memory growth (possible code injection)
            if hasattr(self, '_baseline_memory'):
                memory_growth = memory_info.rss - self._baseline_memory
                if memory_growth > 100 * 1024 * 1024:  # 100MB growth
                    return False, "Suspicious memory growth detected"
            else:
                self._baseline_memory = memory_info.rss
            
            # Check for memory dumps or analysis tools
            suspicious_processes = [
                'processdump', 'procdump', 'memorydump', 'volatility',
                'rekall', 'winpmem', 'dumpit'
            ]
            
            for proc in psutil.process_iter(['name']):
                try:
                    proc_name = proc.info['name'].lower()
                    for suspicious in suspicious_processes:
                        if suspicious in proc_name:
                            return False, f"Memory analysis tool detected: {proc_name}"
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return True, "Memory integrity verified"
            
        except Exception as e:
            return False, f"Memory integrity check failed: {str(e)}"
    
    def verify_runtime_environment(self):
        """Verify runtime environment integrity"""
        try:
            protection_check()
            
            # Check for debugger attachment
            import ctypes
            if ctypes.windll.kernel32.IsDebuggerPresent():
                return False, "Debugger detected"
            
            # Check for remote debugger
            debug_flag = ctypes.c_bool()
            ctypes.windll.kernel32.CheckRemoteDebuggerPresent(
                ctypes.windll.kernel32.GetCurrentProcess(),
                ctypes.byref(debug_flag)
            )
            
            if debug_flag.value:
                return False, "Remote debugger detected"
            
            # Check system uptime (VMs often have low uptime)
            import psutil
            uptime = time.time() - psutil.boot_time()
            if uptime < 300:  # Less than 5 minutes
                return False, "Suspicious system uptime"
            
            # Check for VM indicators
            import platform
            system_info = platform.platform().lower()
            vm_indicators = ['vmware', 'virtualbox', 'vbox', 'qemu', 'xen']
            
            for indicator in vm_indicators:
                if indicator in system_info:
                    return False, f"Virtual machine detected: {indicator}"
            
            return True, "Runtime environment verified"
            
        except Exception as e:
            return False, f"Runtime environment check failed: {str(e)}"
    
    def comprehensive_integrity_check(self):
        """Perform comprehensive integrity verification"""
        try:
            current_time = time.time()
            
            # Don't check too frequently
            if current_time - self._last_verification < 30:
                return True, "Recent verification still valid"
            
            # Perform all integrity checks
            checks = [
                ("Application Integrity", self.verify_application_integrity),
                ("Memory Integrity", self.verify_memory_integrity),
                ("Runtime Environment", self.verify_runtime_environment)
            ]
            
            for check_name, check_func in checks:
                is_valid, message = check_func()
                
                if not is_valid:
                    self._trigger_integrity_violation(f"{check_name} failed: {message}")
                    return False, f"Integrity check failed: {message}"
            
            self._last_verification = current_time
            return True, "All integrity checks passed"
            
        except Exception as e:
            return False, f"Comprehensive integrity check failed: {str(e)}"
    
    def _trigger_integrity_violation(self, reason):
        """Trigger response when integrity violation is detected"""
        try:
            self._verification_active = False
            
            # Log the security event
            self._security._log_security_event(f"Integrity violation: {reason}")
            
            # Clear sensitive data
            self._clear_integrity_data()
            
            # Show misleading error and exit
            import tkinter.messagebox as msgbox
            msgbox.showerror("System Error", 
                           "A critical system error has occurred. The application will now close.\n"
                           "Error Code: 0x80070009")
            
            os._exit(1)
            
        except Exception:
            os._exit(1)
    
    def _clear_integrity_data(self):
        """Clear integrity verification data"""
        try:
            self._known_hashes.clear()
            self._integrity_cache.clear()
        except Exception:
            pass
    
    def start_continuous_monitoring(self):
        """Start continuous integrity monitoring in background"""
        def monitor_loop():
            while self._verification_active:
                try:
                    time.sleep(60)  # Check every minute
                    
                    if self._verification_active:
                        is_valid, message = self.comprehensive_integrity_check()
                        
                        if not is_valid:
                            break
                            
                except Exception:
                    break
        
        # Start monitoring thread
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
    
    def is_verification_active(self):
        """Check if integrity verification is still active"""
        return self._verification_active

# Global integrity verifier instance
_integrity_verifier = None

def get_integrity_verifier():
    """Get or create integrity verifier instance"""
    global _integrity_verifier
    if _integrity_verifier is None:
        _integrity_verifier = IntegrityVerifier()
        # Start continuous monitoring
        _integrity_verifier.start_continuous_monitoring()
    return _integrity_verifier

def verify_application_integrity():
    """Quick application integrity check"""
    try:
        verifier = get_integrity_verifier()
        return verifier.comprehensive_integrity_check()
    except Exception as e:
        return False, f"Integrity verification failed: {str(e)}"

def initialize_integrity_verification():
    """Initialize integrity verification system"""
    try:
        verifier = get_integrity_verifier()
        return verifier.is_verification_active()
    except Exception:
        return False
