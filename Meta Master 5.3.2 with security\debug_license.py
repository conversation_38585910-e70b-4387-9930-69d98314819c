"""
Debug script to test license validation
"""

import os
import sys

def test_license_functions():
    """Test license validation functions"""
    print("🔍 Testing license validation...")
    
    try:
        from license_checker import check_license, load_license_key, get_stable_device_id, LICENSE_FILE
        
        print(f"📁 License file path: {LICENSE_FILE}")
        print(f"📁 License file exists: {os.path.exists(LICENSE_FILE)}")
        
        # Test device ID generation
        device_id = get_stable_device_id()
        print(f"🖥️ Device ID: {device_id[:16]}...")
        
        # Test loading license key
        license_key = load_license_key()
        print(f"🔑 Loaded license key: {license_key[:16] + '...' if license_key else 'None'}")
        
        if license_key:
            # Test license validation
            print("\n🔍 Testing license validation...")
            is_valid, message = check_license(license_key)
            print(f"✅ License valid: {is_valid}")
            print(f"📝 Message: {message}")
            
            if not is_valid:
                print("\n🔍 Testing license validation without key parameter...")
                is_valid2, message2 = check_license()
                print(f"✅ License valid (no param): {is_valid2}")
                print(f"📝 Message (no param): {message2}")
        else:
            print("❌ No license key found")
            
            # Test validation without license
            is_valid, message = check_license()
            print(f"✅ License valid (no key): {is_valid}")
            print(f"📝 Message (no key): {message}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing license functions: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_firebase_connection():
    """Test Firebase connection"""
    print("\n🔥 Testing Firebase connection...")
    
    try:
        import firebase_admin
        from firebase_admin import credentials, firestore
        
        # Check if Firebase is already initialized
        if firebase_admin._apps:
            print("✅ Firebase already initialized")
            db = firestore.client()
            print("✅ Firestore client created")
            
            # Test a simple query
            try:
                # Try to access the licenses collection
                licenses_ref = db.collection("licenses")
                print("✅ Licenses collection accessible")
                return True
            except Exception as e:
                print(f"❌ Error accessing licenses collection: {e}")
                return False
        else:
            print("❌ Firebase not initialized")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Firebase: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_security_protection():
    """Test security protection"""
    print("\n🛡️ Testing security protection...")
    
    try:
        from security_protection import get_security_instance, protection_check
        
        # Test protection check
        protection_check()
        print("✅ Protection check passed")
        
        # Test security instance
        security = get_security_instance()
        print("✅ Security instance created")
        
        # Test environment verification
        env_valid, env_message = security.verify_execution_environment()
        print(f"🌍 Environment valid: {env_valid}")
        print(f"📝 Environment message: {env_message}")
        
        return env_valid
        
    except Exception as e:
        print(f"❌ Error testing security protection: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run license debugging"""
    print("🚀 Meta Master License Debug")
    print("=" * 50)
    
    tests = [
        ("License Functions", test_license_functions),
        ("Firebase Connection", test_firebase_connection),
        ("Security Protection", test_security_protection)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            result = test_func()
            results[test_name] = result
            print(f"{'✅' if result else '❌'} {test_name}: {'PASSED' if result else 'FAILED'}")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            results[test_name] = False
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    all_passed = all(results.values())
    print(f"\n🎯 Overall: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    if not all_passed:
        print("\n💡 Troubleshooting tips:")
        if not results.get("License Functions", True):
            print("   - Check if license file exists and is readable")
            print("   - Verify license key format")
        if not results.get("Firebase Connection", True):
            print("   - Check Firebase configuration")
            print("   - Verify internet connection")
            print("   - Check Firebase credentials")
        if not results.get("Security Protection", True):
            print("   - Check if running in supported environment")
            print("   - Verify security modules are properly installed")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
