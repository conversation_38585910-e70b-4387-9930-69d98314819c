"""
Verify Firebase Security Implementation
"""

import os
import json
import sys

def check_firebase_file_security():
    """Check if Firebase file is secure"""
    print("🔍 Checking Firebase file security...")
    
    firebase_file = "meta-master-firebase.json"
    backup_file = "meta-master-firebase.json.backup"
    
    if not os.path.exists(firebase_file):
        print("❌ Firebase config file not found")
        return False
    
    try:
        with open(firebase_file, 'r') as f:
            config = json.load(f)
        
        # Check if it's a dummy config
        if config.get('project_id') == 'encrypted':
            print("✅ Firebase config is secure (dummy values)")
            
            # Check if backup exists
            if os.path.exists(backup_file):
                print("✅ Original config backed up")
            else:
                print("⚠️ No backup found (may be stored in encrypted storage)")
            
            return True
        else:
            print("❌ Firebase config contains real credentials!")
            print("⚠️ This is a security vulnerability!")
            return False
            
    except Exception as e:
        print(f"❌ Error checking Firebase config: {e}")
        return False

def check_encrypted_storage():
    """Check if Firebase config is in encrypted storage"""
    print("\n🔒 Checking encrypted storage...")
    
    try:
        from secure_config import get_secure_credentials
        
        secure_creds = get_secure_credentials()
        firebase_config = secure_creds.get_firebase_config()
        
        if firebase_config:
            print("✅ Firebase config found in encrypted storage")
            print(f"📋 Project ID: {firebase_config.get('project_id')}")
            print(f"📋 Client Email: {firebase_config.get('client_email')}")
            
            # Verify it's not dummy data
            if firebase_config.get('project_id') != 'encrypted':
                print("✅ Real credentials stored securely")
                return True
            else:
                print("❌ Encrypted storage contains dummy data")
                return False
        else:
            print("❌ No Firebase config in encrypted storage")
            return False
            
    except Exception as e:
        print(f"❌ Error checking encrypted storage: {e}")
        return False

def test_firebase_initialization():
    """Test Firebase initialization with encrypted config"""
    print("\n🔥 Testing Firebase initialization...")
    
    try:
        # Import the initialization function
        import sys
        import os
        
        # Add current directory to path
        sys.path.insert(0, os.getcwd())
        
        from secure_config import get_secure_credentials
        from google.oauth2 import service_account
        
        # Get encrypted config
        secure_creds = get_secure_credentials()
        service_account_data = secure_creds.get_firebase_config()
        
        if not service_account_data:
            print("❌ No Firebase config available")
            return False
        
        # Test credential creation
        SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
        credentials = service_account.Credentials.from_service_account_info(
            service_account_data,
            scopes=SCOPES
        )
        
        print("✅ Firebase credentials created successfully")
        print(f"📋 Project ID: {service_account_data.get('project_id')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Firebase initialization test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_build_extraction():
    """Simulate what an attacker would see in a build"""
    print("\n🕵️ Simulating build extraction attack...")
    
    firebase_file = "meta-master-firebase.json"
    
    if not os.path.exists(firebase_file):
        print("❌ Firebase file not found for simulation")
        return False
    
    try:
        print("   👤 Attacker extracts meta-master-firebase.json...")
        
        with open(firebase_file, 'r') as f:
            extracted_config = json.load(f)
        
        print("   📄 Extracted content:")
        for key, value in extracted_config.items():
            print(f"      {key}: {value}")
        
        # Check if any real credentials are exposed
        sensitive_fields = ['private_key', 'client_email', 'project_id']
        exposed_credentials = []
        
        for field in sensitive_fields:
            if field in extracted_config and extracted_config[field] != 'encrypted':
                exposed_credentials.append(field)
        
        if exposed_credentials:
            print(f"❌ SECURITY BREACH: Exposed credentials: {exposed_credentials}")
            return False
        else:
            print("✅ No real credentials exposed to attacker")
            print("✅ Attacker only sees dummy 'encrypted' values")
            return True
            
    except Exception as e:
        print(f"❌ Simulation error: {e}")
        return False

def main():
    """Run all Firebase security checks"""
    print("🔒 Firebase Security Verification")
    print("=" * 50)
    
    tests = [
        ("Firebase File Security", check_firebase_file_security),
        ("Encrypted Storage", check_encrypted_storage),
        ("Firebase Initialization", test_firebase_initialization),
        ("Build Extraction Simulation", simulate_build_extraction)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} error: {e}")
            results[test_name] = False
    
    print("\n" + "=" * 50)
    print("📊 Security Test Results:")
    
    for test_name, result in results.items():
        status = "✅ SECURE" if result else "❌ VULNERABLE"
        print(f"   {test_name}: {status}")
    
    all_secure = all(results.values())
    
    if all_secure:
        print("\n🎉 ALL SECURITY TESTS PASSED!")
        print("✅ Firebase configuration is completely secure")
        print("✅ No sensitive data exposed in builds")
        print("✅ Encrypted storage working properly")
        print("✅ Safe for public distribution")
    else:
        print("\n⚠️ SECURITY ISSUES DETECTED!")
        print("❌ Some tests failed - check the issues above")
        print("🔧 Run: python encrypt_firebase_config.py")
    
    return all_secure

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
