@echo off
REM Secure Deployment Script for Meta Master
REM This script handles the complete secure build and deployment process

echo ========================================
echo Meta Master Secure Deployment Script
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python and try again
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist ".venv" (
    echo Creating virtual environment...
    python -m venv .venv
)

REM Activate virtual environment
echo Activating virtual environment...
call .venv\Scripts\activate.bat

REM Install/update required packages
echo Installing required packages...
pip install --upgrade pip
pip install -r requirements.txt
pip install cryptography psutil requests pyinstaller

REM Generate integrity data
echo Generating integrity data...
python integrity_verification.py

REM Run secure build
echo Starting secure build process...
python build_secure.py

if errorlevel 1 (
    echo ERROR: Secure build failed
    pause
    exit /b 1
)

REM Check if installer was created
if exist "Output\Meta_Master_Setup_5.1.1.exe" (
    echo.
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo Installer created: Output\Meta_Master_Setup_5.1.1.exe
    echo.
    echo Security features implemented:
    echo - Firebase credentials excluded from client
    echo - Runtime protection enabled
    echo - Code obfuscation applied
    echo - Integrity verification implemented
    echo - Anti-tampering measures active
    echo.
    echo Next steps:
    echo 1. Deploy server_config_endpoint.py to your server
    echo 2. Update server configuration with actual Firebase credentials
    echo 3. Test the installer on a clean system
    echo 4. Consider code signing for additional trust
    echo.
) else (
    echo ERROR: Installer was not created
    echo Please check the build output for errors
    pause
    exit /b 1
)

REM Optional: Open output directory
set /p open_dir="Open output directory? (y/n): "
if /i "%open_dir%"=="y" (
    explorer Output
)

echo.
echo Deployment script completed successfully!
pause
