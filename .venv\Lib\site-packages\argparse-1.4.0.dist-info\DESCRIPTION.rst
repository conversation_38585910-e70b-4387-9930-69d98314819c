The argparse module makes it easy to write user friendly command line
interfaces.

The program defines what arguments it requires, and a<PERSON><PERSON><PERSON> will figure out
how to parse those out of sys.argv. The argparse module also automatically
generates help and usage messages and issues errors when users give the
program invalid arguments.

As of Python >= 2.7 and >= 3.2, the argparse module is maintained within the
Python standard library. For users who still need to support Python < 2.7 or
< 3.2, it is also provided as a separate package, which tries to stay
compatible with the module in the standard library, but also supports older
Python versions.

Also, we can fix bugs here for users who are stuck on some non-current python
version, like e.g. 3.2.3 (which has bugs that were fixed in a later 3.2.x
release).

argparse is licensed under the Python license, for details see LICENSE.txt.


Compatibility
-------------

argparse should work on Python >= 2.3, it was tested on:

* 2.3, 2.4, 2.5, 2.6 and 2.7
* 3.1, 3.2, 3.3, 3.4


Installation
------------

Try one of these:

    python setup.py install

    easy_install argparse

    pip install argparse

    putting argparse.py in some directory listed in sys.path should also work


Bugs
----

If you find a bug in argparse (pypi), please try to reproduce it with latest
python 2.7 and 3.4 (and use argparse from stdlib).

If it happens there also, please file a bug in the python.org issue tracker.
If it does not happen there, file a bug in the argparse package issue tracker.



