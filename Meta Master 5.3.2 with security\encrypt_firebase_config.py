"""
Encrypt Firebase Configuration for Meta Master
This script encrypts the Firebase JSON file and integrates it with the secure credential system
"""

import os
import json
import sys
from secure_config import get_secure_credentials

def encrypt_firebase_config():
    """Encrypt the Firebase configuration file"""
    print("🔒 Encrypting Firebase configuration...")
    
    firebase_file = "meta-master-firebase.json"
    
    if not os.path.exists(firebase_file):
        print(f"❌ Firebase file not found: {firebase_file}")
        return False
    
    try:
        # Load the Firebase configuration
        with open(firebase_file, 'r') as f:
            firebase_config = json.load(f)
        
        print("✅ Firebase configuration loaded")
        
        # Get secure credentials manager
        secure_creds = get_secure_credentials()
        
        # Store the Firebase configuration securely
        success = secure_creds.store_firebase_config(firebase_config)
        
        if success:
            print("✅ Firebase configuration encrypted and stored securely")
            
            # Create a backup of the original file
            backup_file = firebase_file + ".backup"
            os.rename(firebase_file, backup_file)
            print(f"✅ Original file backed up as: {backup_file}")
            
            # Create a dummy file for the build process
            dummy_config = ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            
            with open(firebase_file, 'w') as f:
                json.dump(dummy_config, f, indent=2)
            
            print("✅ Dummy configuration file created for build process")
            
            return True
        else:
            print("❌ Failed to store Firebase configuration securely")
            return False
            
    except Exception as e:
        print(f"❌ Error encrypting Firebase configuration: {e}")
        return False

def test_encrypted_config():
    """Test if the encrypted configuration can be retrieved"""
    print("\n🧪 Testing encrypted configuration retrieval...")
    
    try:
        from secure_config import get_secure_credentials
        
        secure_creds = get_secure_credentials()
        firebase_config = secure_creds.get_firebase_config()
        
        if firebase_config:
            print("✅ Encrypted Firebase configuration retrieved successfully")
            print(f"📋 Project ID: {firebase_config.get('project_id', 'Unknown')}")
            print(f"📋 Client Email: {firebase_config.get('client_email', 'Unknown')}")
            return True
        else:
            print("❌ Failed to retrieve encrypted Firebase configuration")
            return False
            
    except Exception as e:
        print(f"❌ Error testing encrypted configuration: {e}")
        return False

def restore_original_config():
    """Restore the original Firebase configuration from backup"""
    print("\n🔄 Restoring original Firebase configuration...")
    
    firebase_file = "meta-master-firebase.json"
    backup_file = firebase_file + ".backup"
    
    if os.path.exists(backup_file):
        try:
            os.rename(backup_file, firebase_file)
            print("✅ Original Firebase configuration restored")
            return True
        except Exception as e:
            print(f"❌ Error restoring configuration: {e}")
            return False
    else:
        print("❌ Backup file not found")
        return False

def main():
    """Main encryption process"""
    print("🔒 Firebase Configuration Encryption Tool")
    print("=" * 50)
    
    # Step 1: Encrypt the configuration
    if not encrypt_firebase_config():
        print("❌ Encryption failed!")
        return False
    
    # Step 2: Test the encrypted configuration
    if not test_encrypted_config():
        print("❌ Testing failed!")
        # Restore original if test fails
        restore_original_config()
        return False
    
    print("\n" + "=" * 50)
    print("🎉 Firebase configuration encryption completed successfully!")
    print("\n📋 What happened:")
    print("   ✅ Original Firebase config encrypted and stored securely")
    print("   ✅ Original file backed up as .backup")
    print("   ✅ Dummy config created for build process")
    print("   ✅ Encryption tested and verified")
    
    print("\n🚀 Next steps:")
    print("   1. The application will now load Firebase config from secure storage")
    print("   2. The dummy config file can be safely included in builds")
    print("   3. Your Firebase credentials are now protected!")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
