"""
Runtime Protection Hooks for Meta Master
Implements runtime anti-tampering and integrity checks
"""

import os
import sys
import time
import hashlib
import threading
import random
import functools
from security_protection import get_security_instance, protection_check

class RuntimeProtection:
    def __init__(self):
        self._protection_active = True
        self._function_hashes = {}
        self._call_counts = {}
        self._last_check = time.time()
        self._security = get_security_instance()
        
    def protect_function(self, func):
        """Decorator to add runtime protection to critical functions"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                # Quick protection check
                if not self._protection_active:
                    return None
                
                # Verify security protection is still active
                protection_check()
                
                # Add random delay to prevent timing attacks
                time.sleep(random.uniform(0.001, 0.005))
                
                # Check function integrity
                if not self._verify_function_integrity(func):
                    self._trigger_protection_response("Function integrity compromised")
                    return None
                
                # Monitor call frequency
                func_name = func.__name__
                current_time = time.time()
                
                if func_name not in self._call_counts:
                    self._call_counts[func_name] = []
                
                # Remove old calls (older than 1 minute)
                self._call_counts[func_name] = [
                    call_time for call_time in self._call_counts[func_name] 
                    if current_time - call_time < 60
                ]
                
                # Add current call
                self._call_counts[func_name].append(current_time)
                
                # Check for suspicious call frequency
                if len(self._call_counts[func_name]) > 100:  # More than 100 calls per minute
                    self._trigger_protection_response(f"Suspicious call frequency for {func_name}")
                    return None
                
                # Execute the function
                result = func(*args, **kwargs)
                
                # Add another random delay
                time.sleep(random.uniform(0.001, 0.003))
                
                return result
                
            except Exception as e:
                # Log security event
                try:
                    self._security._log_security_event(f"Runtime protection error in {func.__name__}: {str(e)}")
                except Exception:
                    pass
                
                # Return None on any protection error
                return None
                
        return wrapper
    
    def _verify_function_integrity(self, func):
        """Verify that a function hasn't been tampered with"""
        try:
            # Get function source code hash
            import inspect
            source = inspect.getsource(func)
            current_hash = hashlib.sha256(source.encode()).hexdigest()
            
            func_name = func.__name__
            
            if func_name not in self._function_hashes:
                # First time seeing this function, store its hash
                self._function_hashes[func_name] = current_hash
                return True
            
            # Compare with stored hash
            return self._function_hashes[func_name] == current_hash
            
        except Exception:
            # If we can't verify, assume it's compromised
            return False
    
    def _trigger_protection_response(self, reason):
        """Trigger protection response when threat detected"""
        try:
            self._protection_active = False
            
            # Log the security event
            self._security._log_security_event(f"Runtime protection triggered: {reason}")
            
            # Clear sensitive data
            self._clear_runtime_data()
            
            # Show misleading error and exit
            import tkinter.messagebox as msgbox
            msgbox.showerror("System Error", 
                           "A critical system error has occurred. The application will now close.\n"
                           "Error Code: 0x80070006")
            
            os._exit(1)
            
        except Exception:
            os._exit(1)
    
    def _clear_runtime_data(self):
        """Clear sensitive runtime data"""
        try:
            self._function_hashes.clear()
            self._call_counts.clear()
        except Exception:
            pass
    
    def periodic_integrity_check(self):
        """Perform periodic integrity checks"""
        while self._protection_active:
            try:
                current_time = time.time()
                
                # Check every 30 seconds
                if current_time - self._last_check > 30:
                    protection_check()
                    self._last_check = current_time
                
                time.sleep(5)
                
            except Exception:
                self._trigger_protection_response("Periodic integrity check failed")
                break

# Global runtime protection instance
_runtime_protection = None

def get_runtime_protection():
    """Get or create runtime protection instance"""
    global _runtime_protection
    if _runtime_protection is None:
        _runtime_protection = RuntimeProtection()
        # Start periodic checks in background
        threading.Thread(target=_runtime_protection.periodic_integrity_check, daemon=True).start()
    return _runtime_protection

def runtime_protect(func):
    """Decorator to add runtime protection to functions"""
    return get_runtime_protection().protect_function(func)

# Anti-debugging and anti-analysis functions
def anti_debug_check():
    """Check for debugging attempts"""
    try:
        import ctypes
        
        # Check if debugger is present
        if ctypes.windll.kernel32.IsDebuggerPresent():
            return False
        
        # Check for remote debugger
        debug_flag = ctypes.c_bool()
        ctypes.windll.kernel32.CheckRemoteDebuggerPresent(
            ctypes.windll.kernel32.GetCurrentProcess(),
            ctypes.byref(debug_flag)
        )
        
        if debug_flag.value:
            return False
        
        return True
        
    except Exception:
        return False

def anti_vm_check():
    """Check for virtual machine environment"""
    try:
        import platform
        import subprocess
        
        # Check system information for VM indicators
        system_info = platform.platform().lower()
        vm_indicators = ['vmware', 'virtualbox', 'vbox', 'qemu', 'xen', 'hyper-v']
        
        for indicator in vm_indicators:
            if indicator in system_info:
                return False
        
        # Check for VM-specific registry keys (Windows)
        try:
            result = subprocess.run(['reg', 'query', 'HKLM\\SYSTEM\\CurrentControlSet\\Services\\VBoxService'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                return False
        except Exception:
            pass
        
        return True
        
    except Exception:
        return True  # Assume legitimate if check fails

def memory_protection_check():
    """Check for memory analysis tools"""
    try:
        import psutil
        
        suspicious_processes = [
            'cheatengine', 'processhacker', 'procmon', 'procexp',
            'ollydbg', 'x64dbg', 'windbg', 'ida', 'ghidra',
            'wireshark', 'fiddler', 'charles'
        ]
        
        for proc in psutil.process_iter(['name']):
            try:
                proc_name = proc.info['name'].lower()
                for suspicious in suspicious_processes:
                    if suspicious in proc_name:
                        return False
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return True
        
    except Exception:
        return True  # Assume safe if check fails

def comprehensive_protection_check():
    """Perform comprehensive protection checks"""
    try:
        # Run all protection checks
        checks = [
            anti_debug_check(),
            anti_vm_check(),
            memory_protection_check()
        ]
        
        # If any check fails, trigger protection
        if not all(checks):
            get_security_instance()._trigger_protection_response("Comprehensive protection check failed")
            return False
        
        return True
        
    except Exception:
        return False

# Initialize runtime protection
def initialize_runtime_protection():
    """Initialize runtime protection system"""
    try:
        # Perform initial checks
        if not comprehensive_protection_check():
            return False
        
        # Initialize runtime protection
        get_runtime_protection()
        
        return True
        
    except Exception:
        return False
