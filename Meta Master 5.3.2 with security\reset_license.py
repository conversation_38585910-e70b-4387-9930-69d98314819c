"""
License Reset Tool for Meta Master
Use this if you need to re-activate your existing license key
"""

import os
import sys

def reset_license_data():
    """Reset license data to allow re-activation"""
    print("🔄 License Reset Tool")
    print("=" * 50)
    
    try:
        from license_checker import LICENSE_FILE, DEVICE_ID_FILE
        
        files_to_reset = [
            (LICENSE_FILE, "License file"),
            (DEVICE_ID_FILE, "Device ID file")
        ]
        
        reset_count = 0
        
        for file_path, description in files_to_reset:
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    print(f"✅ Removed {description}: {file_path}")
                    reset_count += 1
                except Exception as e:
                    print(f"❌ Failed to remove {description}: {e}")
            else:
                print(f"ℹ️ {description} not found: {file_path}")
        
        if reset_count > 0:
            print(f"\n✅ Reset {reset_count} license files")
            print("💡 You can now re-enter your license key in Meta Master")
        else:
            print("\n💡 No license files found to reset")
        
        return True
        
    except Exception as e:
        print(f"❌ Error resetting license: {e}")
        return False

def backup_current_license():
    """Backup current license before reset"""
    print("\n💾 Backing up current license...")
    
    try:
        from license_checker import load_license_key, get_stable_device_id
        
        current_key = load_license_key()
        current_device = get_stable_device_id()
        
        if current_key:
            print(f"🔑 Current license key: {current_key}")
            print(f"🖥️ Current device ID: {current_device[:16]}...")
            
            # Save to backup file
            backup_file = "license_backup.txt"
            with open(backup_file, 'w') as f:
                f.write(f"License Key: {current_key}\n")
                f.write(f"Device ID: {current_device}\n")
                f.write(f"Backup Date: {datetime.datetime.now()}\n")
            
            print(f"✅ License backed up to: {backup_file}")
            return current_key
        else:
            print("ℹ️ No license key found to backup")
            return None
            
    except Exception as e:
        print(f"❌ Error backing up license: {e}")
        return None

def test_license_after_reset():
    """Test license functionality after reset"""
    print("\n🧪 Testing license system after reset...")
    
    try:
        from license_checker import check_license
        
        # This should fail since we reset everything
        is_valid, message = check_license()
        
        if not is_valid:
            print("✅ License system ready for new activation")
            print(f"📝 Status: {message}")
            return True
        else:
            print("⚠️ License still active after reset")
            return False
            
    except Exception as e:
        print(f"❌ Error testing license: {e}")
        return False

def main():
    """Main reset function"""
    print("🔄 Meta Master License Reset Tool")
    print("=" * 60)
    print()
    print("This tool will reset your license data so you can re-activate")
    print("your existing license key if it's not working properly.")
    print()
    
    # Ask for confirmation
    response = input("Do you want to proceed with license reset? (y/N): ").strip().lower()
    
    if response not in ['y', 'yes']:
        print("❌ License reset cancelled")
        return False
    
    print("\n🔄 Starting license reset process...")
    
    # Step 1: Backup current license
    import datetime
    current_key = backup_current_license()
    
    # Step 2: Reset license data
    if reset_license_data():
        print("\n✅ License reset completed successfully!")
        
        # Step 3: Test system
        test_license_after_reset()
        
        print("\n" + "=" * 60)
        print("🎯 Next Steps:")
        print("1. Run Meta Master: python \"Meta Master.py\"")
        print("2. Enter your license key when prompted")
        if current_key:
            print(f"3. Your license key: {current_key}")
        print("4. The license should activate normally")
        
        return True
    else:
        print("\n❌ License reset failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
