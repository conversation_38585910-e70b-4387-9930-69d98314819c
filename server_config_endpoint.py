"""
Server-side Configuration Endpoint for Meta Master
This should be deployed on your secure server (e.g., getmetamaster.com)
Handles secure delivery of Firebase credentials and other sensitive configurations
"""

from flask import Flask, request, jsonify
import hashlib
import time
import json
import base64
import os
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

app = Flask(__name__)

# Configuration (use environment variables in production)
SECRET_KEY = os.getenv('CONFIG_SECRET_KEY', 'your-secret-key-here')
FIREBASE_CONFIG = **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# Valid app signatures (in production, store in database)
VALID_APP_SIGNATURES = {
    "your-app-signature-1": {"version": "5.1.1", "active": True},
    "your-app-signature-2": {"version": "5.1.0", "active": True},
    # Add more as needed
}

class ConfigServer:
    def __init__(self):
        self.rate_limits = {}  # Simple rate limiting
        
    def _derive_key(self, password: str, salt: bytes) -> bytes:
        """Derive encryption key from password and salt"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        return base64.urlsafe_b64encode(kdf.derive(password.encode()))
    
    def _encrypt_data(self, data: str, password: str) -> str:
        """Encrypt data with password"""
        salt = os.urandom(16)
        key = self._derive_key(password, salt)
        f = Fernet(key)
        encrypted_data = f.encrypt(data.encode())
        return base64.b64encode(salt + encrypted_data).decode()
    
    def _validate_request(self, data: dict) -> tuple:
        """Validate incoming request"""
        required_fields = ['app_signature', 'device_id', 'timestamp', 'signature']
        
        # Check required fields
        for field in required_fields:
            if field not in data:
                return False, f"Missing required field: {field}"
        
        # Check timestamp (prevent replay attacks)
        current_time = int(time.time())
        request_time = data.get('timestamp', 0)
        
        if abs(current_time - request_time) > 300:  # 5 minutes tolerance
            return False, "Request timestamp too old or too far in future"
        
        # Validate app signature
        app_signature = data.get('app_signature')
        if app_signature not in VALID_APP_SIGNATURES:
            return False, "Invalid app signature"
        
        app_info = VALID_APP_SIGNATURES[app_signature]
        if not app_info.get('active', False):
            return False, "App signature is deactivated"
        
        # Validate request signature
        expected_signature = hashlib.sha256(
            f"{app_signature}:{data['device_id']}:{request_time}".encode()
        ).hexdigest()
        
        if data.get('signature') != expected_signature:
            return False, "Invalid request signature"
        
        return True, "Valid request"
    
    def _check_rate_limit(self, device_id: str) -> bool:
        """Simple rate limiting per device"""
        current_time = time.time()
        
        if device_id not in self.rate_limits:
            self.rate_limits[device_id] = []
        
        # Clean old requests (older than 1 hour)
        self.rate_limits[device_id] = [
            req_time for req_time in self.rate_limits[device_id]
            if current_time - req_time < 3600
        ]
        
        # Check if too many requests
        if len(self.rate_limits[device_id]) >= 10:  # Max 10 requests per hour
            return False
        
        # Add current request
        self.rate_limits[device_id].append(current_time)
        return True
    
    def get_firebase_config(self, request_data: dict) -> dict:
        """Get Firebase configuration for valid requests"""
        try:
            # Validate request
            is_valid, message = self._validate_request(request_data)
            if not is_valid:
                return {
                    "success": False,
                    "error": message,
                    "code": "INVALID_REQUEST"
                }
            
            # Check rate limiting
            device_id = request_data.get('device_id')
            if not self._check_rate_limit(device_id):
                return {
                    "success": False,
                    "error": "Rate limit exceeded",
                    "code": "RATE_LIMIT"
                }
            
            # Encrypt configuration
            app_signature = request_data.get('app_signature')
            encryption_key = f"{app_signature}:{device_id}"
            
            config_json = json.dumps(FIREBASE_CONFIG)
            encrypted_config = self._encrypt_data(config_json, encryption_key)
            
            return {
                "success": True,
                "config": encrypted_config,
                "timestamp": int(time.time())
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": "Internal server error",
                "code": "SERVER_ERROR"
            }

config_server = ConfigServer()

@app.route('/api/config', methods=['POST'])
def get_config():
    """API endpoint for configuration requests"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided",
                "code": "NO_DATA"
            }), 400
        
        config_type = data.get('config_type', 'firebase')
        
        if config_type == 'firebase':
            result = config_server.get_firebase_config(data)
            status_code = 200 if result.get('success') else 400
            return jsonify(result), status_code
        else:
            return jsonify({
                "success": False,
                "error": "Unsupported config type",
                "code": "UNSUPPORTED_TYPE"
            }), 400
            
    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Server error",
            "code": "SERVER_ERROR"
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "timestamp": int(time.time()),
        "service": "Meta Master Config Server"
    })

@app.route('/api/version', methods=['GET'])
def version_info():
    """Version information endpoint"""
    return jsonify({
        "service": "Meta Master Config Server",
        "version": "1.0.0",
        "supported_app_versions": list(set(
            info["version"] for info in VALID_APP_SIGNATURES.values()
        ))
    })

if __name__ == '__main__':
    # Development server (use proper WSGI server in production)
    app.run(debug=False, host='0.0.0.0', port=5000)

"""
Deployment Instructions:

1. Install dependencies:
   pip install flask cryptography

2. Set environment variables:
   export CONFIG_SECRET_KEY="your-very-secure-secret-key"
   
3. Update FIREBASE_CONFIG with your actual Firebase credentials

4. Update VALID_APP_SIGNATURES with your app's actual signatures

5. Deploy to your server (e.g., using gunicorn):
   gunicorn -w 4 -b 0.0.0.0:5000 server_config_endpoint:app

6. Set up reverse proxy (nginx) with SSL:
   server {
       listen 443 ssl;
       server_name getmetamaster.com;
       
       location /api/ {
           proxy_pass http://localhost:5000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }

7. Test the endpoint:
   curl -X POST https://getmetamaster.com/api/config \
        -H "Content-Type: application/json" \
        -d '{"app_signature":"test","device_id":"test","timestamp":1234567890,"signature":"test","config_type":"firebase"}'
"""
