#!/bin/bash
# Secure Deployment Script for Meta Master
# This script handles the complete secure build and deployment process

echo "========================================"
echo "Meta Master Secure Deployment Script"
echo "========================================"
echo

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "ERROR: Python 3 is not installed or not in PATH"
    echo "Please install Python 3 and try again"
    exit 1
fi

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv .venv
fi

# Activate virtual environment
echo "Activating virtual environment..."
source .venv/bin/activate

# Install/update required packages
echo "Installing required packages..."
pip install --upgrade pip

# Fix pathlib conflict (common PyInstaller issue)
echo "Checking for pathlib conflicts..."
pip uninstall pathlib -y >/dev/null 2>&1
echo "Pathlib conflict resolved (if existed)"

# Install required packages
pip install -r requirements.txt
pip install cryptography psutil requests pyinstaller

# Verify PyInstaller installation
echo "Verifying PyInstaller installation..."
python -m PyInstaller --version
if [ $? -ne 0 ]; then
    echo "ERROR: PyInstaller installation failed"
    exit 1
fi

# Generate integrity data
echo "Generating integrity data..."
python integrity_verification.py

# Run secure build
echo "Starting secure build process..."
python build_secure.py

if [ $? -ne 0 ]; then
    echo "ERROR: Secure build failed"
    exit 1
fi

# Check if installer was created (for Windows builds on Linux/Mac)
if [ -f "Output/Meta_Master_Setup_5.3.2.exe" ]; then
    echo
    echo "========================================"
    echo "BUILD SUCCESSFUL!"
    echo "========================================"
    echo
    echo "Installer created: Output/Meta_Master_Setup_5.3.2.exe"
    echo
    echo "Security features implemented:"
    echo "- Firebase credentials excluded from client"
    echo "- Runtime protection enabled"
    echo "- Code obfuscation applied"
    echo "- Integrity verification implemented"
    echo "- Anti-tampering measures active"
    echo
    echo "Next steps:"
    echo "1. Deploy server_config_endpoint.py to your server"
    echo "2. Update server configuration with actual Firebase credentials"
    echo "3. Test the installer on a clean system"
    echo "4. Consider code signing for additional trust"
    echo
elif [ -f "dist/Meta Master" ]; then
    echo
    echo "========================================"
    echo "BUILD SUCCESSFUL!"
    echo "========================================"
    echo
    echo "Executable created: dist/Meta Master"
    echo "(Note: Inno Setup installer creation requires Windows)"
    echo
else
    echo "ERROR: Build output not found"
    echo "Please check the build output for errors"
    exit 1
fi

# Optional: Open output directory
read -p "Open output directory? (y/n): " open_dir
if [[ $open_dir == "y" || $open_dir == "Y" ]]; then
    if command -v xdg-open &> /dev/null; then
        xdg-open Output 2>/dev/null || xdg-open dist 2>/dev/null
    elif command -v open &> /dev/null; then
        open Output 2>/dev/null || open dist 2>/dev/null
    fi
fi

echo
echo "Deployment script completed successfully!"
