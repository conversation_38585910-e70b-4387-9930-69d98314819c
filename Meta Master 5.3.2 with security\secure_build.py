"""
Secure Build Script for Meta Master
Implements additional security measures during the build process
"""

import os
import sys
import subprocess
import hashlib
import time
import shutil
import tempfile
import json
from pathlib import Path

class SecureBuildManager:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.build_dir = self.project_root / "build"
        self.dist_dir = self.project_root / "dist"
        self.spec_file = self.project_root / "Meta Master.spec"
        self.main_script = self.project_root / "Meta Master.py"
        
    def clean_build_directories(self):
        """Clean previous build artifacts"""
        print("🧹 Cleaning build directories...")
        
        directories_to_clean = [self.build_dir, self.dist_dir]
        
        for directory in directories_to_clean:
            if directory.exists():
                try:
                    shutil.rmtree(directory)
                    print(f"   ✅ Cleaned {directory}")
                except Exception as e:
                    print(f"   ⚠️ Warning: Could not clean {directory}: {e}")
    
    def verify_dependencies(self):
        """Verify all required dependencies are installed"""
        print("📦 Verifying dependencies...")
        
        required_packages = [
            'pyinstaller',
            'cryptography',
            'psutil',
            'ttkbootstrap',
            'firebase-admin',
            'google-generativeai',
            'pillow',
            'opencv-python',
            'cairosvg',
            'requests'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                print(f"   ✅ {package}")
            except ImportError:
                missing_packages.append(package)
                print(f"   ❌ {package} - MISSING")
        
        if missing_packages:
            print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
            print("Please install missing packages with: pip install " + " ".join(missing_packages))
            return False
        
        return True
    
    def obfuscate_source_code(self):
        """Apply basic source code obfuscation"""
        print("🔒 Applying source code obfuscation...")
        
        try:
            # Create obfuscated copies of security modules
            security_files = [
                'security_protection.py',
                'secure_config.py',
                'license_validation.py',
                'runtime_protection.py'
            ]
            
            for file_name in security_files:
                file_path = self.project_root / file_name
                if file_path.exists():
                    self._obfuscate_file(file_path)
                    print(f"   ✅ Obfuscated {file_name}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Obfuscation failed: {e}")
            return False
    
    def _obfuscate_file(self, file_path):
        """Apply basic obfuscation to a Python file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Create backup
            backup_path = file_path.with_suffix('.py.backup')
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # Apply basic obfuscation (remove comments, compress whitespace)
            lines = content.split('\n')
            obfuscated_lines = []
            
            for line in lines:
                # Remove comments but preserve docstrings
                if line.strip().startswith('#') and '"""' not in line and "'''" not in line:
                    continue
                
                # Remove inline comments (basic)
                if '#' in line and not line.strip().startswith('#'):
                    # Simple check to avoid breaking strings with #
                    if line.count('"') % 2 == 0 and line.count("'") % 2 == 0:
                        line = line.split('#')[0].rstrip()
                
                obfuscated_lines.append(line)
            
            # Write obfuscated content
            obfuscated_content = '\n'.join(obfuscated_lines)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(obfuscated_content)
                
        except Exception as e:
            print(f"Error obfuscating {file_path}: {e}")
    
    def create_version_info(self):
        """Create version info file for the executable"""
        print("📝 Creating version info...")
        
        version_info = """
# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(5, 3, 2, 0),
    prodvers=(5, 3, 2, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Meta Master Software'),
        StringStruct(u'FileDescription', u'Meta Master - AI-Powered Metadata Generator'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'Meta Master'),
        StringStruct(u'LegalCopyright', u'Copyright © 2024 Meta Master Software. All rights reserved.'),
        StringStruct(u'OriginalFilename', u'Meta Master.exe'),
        StringStruct(u'ProductName', u'Meta Master'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
"""
        
        version_file = self.project_root / "version_info.txt"
        with open(version_file, 'w', encoding='utf-8') as f:
            f.write(version_info.strip())
        
        print("   ✅ Version info created")
        return True
    
    def enhanced_build_executable(self):
        """Enhanced build with additional protection"""
        print("🔨 Building with enhanced protection...")
        
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            '--onefile',                    # Single file (harder to extract)
            '--windowed',
            '--strip',                      # Remove debug symbols
            '--upx',                        # Compress with UPX
            '--key', self._generate_build_key(),  # Add encryption key
            '--exclude-module', 'pdb',
            '--exclude-module', 'unittest',
            '--exclude-module', 'doctest',
            str(self.spec_file)
        ]
        
        # Run with encryption
        result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
        return result.returncode == 0

    def _generate_build_key(self):
        """Generate encryption key for PyInstaller"""
        import secrets
        return secrets.token_hex(16)
    
    def verify_executable(self):
        """Verify the built executable"""
        print("🔍 Verifying executable...")
        
        exe_path = self.dist_dir / "Meta Master.exe"
        
        if not exe_path.exists():
            print("   ❌ Executable not found")
            return False
        
        # Check file size (should be reasonable)
        file_size = exe_path.stat().st_size
        size_mb = file_size / (1024 * 1024)
        
        print(f"   📏 Executable size: {size_mb:.1f} MB")
        
        if size_mb < 50:  # Too small, probably missing dependencies
            print("   ⚠️ Warning: Executable seems too small")
        elif size_mb > 500:  # Too large
            print("   ⚠️ Warning: Executable seems too large")
        else:
            print("   ✅ Executable size looks good")
        
        # Calculate hash
        with open(exe_path, 'rb') as f:
            exe_hash = hashlib.sha256(f.read()).hexdigest()
        
        print(f"   🔐 SHA256: {exe_hash}")
        
        # Save build info
        build_info = {
            'build_time': time.time(),
            'file_size': file_size,
            'sha256': exe_hash,
            'version': '5.3.2'
        }
        
        info_file = self.dist_dir / "build_info.json"
        with open(info_file, 'w') as f:
            json.dump(build_info, f, indent=2)
        
        return True
    
    def restore_source_files(self):
        """Restore original source files from backups"""
        print("🔄 Restoring source files...")
        
        backup_files = list(self.project_root.glob("*.py.backup"))
        
        for backup_file in backup_files:
            original_file = backup_file.with_suffix('')
            try:
                shutil.copy2(backup_file, original_file)
                backup_file.unlink()  # Remove backup
                print(f"   ✅ Restored {original_file.name}")
            except Exception as e:
                print(f"   ⚠️ Warning: Could not restore {original_file.name}: {e}")
    
    def secure_firebase_config(self):
        """Ensure Firebase configuration is secured"""
        print("🔒 Securing Firebase configuration...")

        try:
            firebase_file = self.project_root / "meta-master-firebase.json"
            backup_file = self.project_root / "meta-master-firebase.json.backup"

            # Check if we have a backup (real config)
            if backup_file.exists():
                print("   ✅ Real Firebase config already backed up")
            else:
                # Check if current file contains real credentials
                if firebase_file.exists():
                    with open(firebase_file, 'r') as f:
                        config = json.load(f)

                    if config.get('project_id') != 'encrypted':
                        print("   ⚠️ Real Firebase config detected, encrypting...")
                        # Run encryption script
                        result = subprocess.run([sys.executable, 'encrypt_firebase_config.py'],
                                              cwd=self.project_root, capture_output=True, text=True)
                        if result.returncode != 0:
                            print(f"   ❌ Firebase encryption failed: {result.stderr}")
                            return False
                        print("   ✅ Firebase config encrypted successfully")
                    else:
                        print("   ✅ Firebase config already encrypted")
                else:
                    print("   ❌ Firebase config file not found")
                    return False

            return True

        except Exception as e:
            print(f"   ❌ Firebase security error: {e}")
            return False

    def secure_build(self):
        """Perform complete secure build process"""
        print("🚀 Starting secure build process for Meta Master...")
        print("=" * 60)

        steps = [
            ("Clean build directories", self.clean_build_directories),
            ("Verify dependencies", self.verify_dependencies),
            ("Secure Firebase config", self.secure_firebase_config),
            ("Create version info", self.create_version_info),
            ("Obfuscate source code", self.obfuscate_source_code),
            ("Build executable", self.build_executable),
            ("Verify executable", self.verify_executable),
            ("Restore source files", self.restore_source_files)
        ]
        
        for step_name, step_func in steps:
            print(f"\n📋 {step_name}...")
            
            try:
                if not step_func():
                    print(f"❌ Build failed at step: {step_name}")
                    return False
            except Exception as e:
                print(f"❌ Error in step '{step_name}': {e}")
                return False
        
        print("\n" + "=" * 60)
        print("🎉 Secure build completed successfully!")
        print(f"📁 Executable location: {self.dist_dir / 'Meta Master.exe'}")
        
        return True

def main():
    """Main build function"""
    builder = SecureBuildManager()
    success = builder.secure_build()
    
    if success:
        print("\n✅ Build process completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Build process failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()

